# 校园二手书交易系统

一个基于 Vue 3 + Vite + Element Plus 的校园二手书交易平台前端项目。

## 项目简介

本项目是一个完整的校园二手书交易系统前端应用，支持用户注册登录、图书浏览购买、发布图书、订单管理等功能。采用现代化的前端技术栈，提供良好的用户体验和响应式设计。

## 技术栈

- **框架**: Vue 3 (Composition API)
- **构建工具**: Vite
- **UI 框架**: Element Plus
- **状态管理**: Pinia
- **路由管理**: Vue Router 4
- **HTTP 客户端**: Axios
- **开发语言**: TypeScript
- **样式**: CSS3 + 响应式设计

## 功能特性

### 用户功能
- ✅ 用户注册与登录
- ✅ 图书浏览与搜索
- ✅ 图书分类筛选
- ✅ 图书详情查看
- ✅ 图书购买功能
- ✅ 发布二手图书
- ✅ 我的发布管理
- ✅ 订单记录查看
- ✅ 响应式移动端适配

### 系统特性
- ✅ 用户权限管理
- ✅ 图书状态管理
- ✅ Mock 数据模拟
- ✅ 统一错误处理
- ✅ 路由守卫
- ✅ 组件化开发

## 项目结构

```
src/
├── api/                    # API 接口
│   ├── auth.ts            # 用户认证接口
│   ├── books.ts           # 图书相关接口
│   └── orders.ts          # 订单相关接口
├── assets/                # 静态资源
├── components/            # 公共组件
│   └── common/           # 通用组件
│       ├── AppHeader.vue  # 应用头部
│       └── BookCard.vue   # 图书卡片
├── mock/                  # Mock 数据
│   └── data.ts           # 模拟数据
├── router/                # 路由配置
│   └── index.ts          # 路由定义
├── stores/                # 状态管理
│   ├── index.ts          # Pinia 配置
│   ├── user.ts           # 用户状态
│   └── books.ts          # 图书状态
├── types/                 # TypeScript 类型定义
│   └── index.ts          # 类型声明
├── utils/                 # 工具函数
│   └── request.ts        # HTTP 请求封装
├── views/                 # 页面组件
│   ├── Home.vue          # 首页
│   ├── Login.vue         # 登录页
│   ├── Register.vue      # 注册页
│   ├── Publish.vue       # 发布图书页
│   ├── MyBooks.vue       # 我的发布页
│   ├── Orders.vue        # 订单页
│   ├── BookDetail.vue    # 图书详情页
│   └── NotFound.vue      # 404页面
├── App.vue               # 根组件
└── main.ts               # 应用入口
```

## 快速开始

### 环境要求
- Node.js >= 16.0.0
- npm >= 7.0.0

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

访问 http://localhost:3000 查看应用

### 构建生产版本
```bash
npm run build
```

### 预览生产构建
```bash
npm run preview
```

## 演示账户

系统提供以下演示账户供测试使用：

- **管理员账户**: admin / 123456
- **普通用户**: student1 / 123456
- **普通用户**: student2 / 123456

## 页面路由

| 路径 | 页面 | 说明 | 权限要求 |
|------|------|------|----------|
| `/` | 首页 | 图书浏览和搜索 | 无 |
| `/login` | 登录页 | 用户登录 | 无 |
| `/register` | 注册页 | 用户注册 | 无 |
| `/publish` | 发布图书 | 发布二手图书 | 需登录 |
| `/my-books` | 我的发布 | 管理发布的图书 | 需登录 |
| `/orders` | 我的订单 | 查看购买和销售记录 | 需登录 |
| `/book/:id` | 图书详情 | 查看图书详细信息 | 无 |

## 核心功能说明

### 1. 用户认证
- 支持用户注册和登录
- JWT Token 认证机制
- 自动登录状态保持
- 路由权限控制

### 2. 图书管理
- 图书列表展示和分页
- 多条件搜索和筛选
- 图书详情查看
- 图书状态管理（在售/已售出）

### 3. 交易功能
- 一键购买图书
- 订单记录管理
- 买家和卖家订单分离
- 交易状态跟踪

### 4. 发布功能
- 图书信息发布
- 图片上传（模拟）
- 发布记录管理
- 图书下架功能

## 开发说明

### Mock 数据
项目使用本地 Mock 数据模拟后端接口，所有数据存储在内存中。实际部署时需要：
1. 替换 API 接口为真实后端地址
2. 实现图片上传功能
3. 添加数据持久化

### 响应式设计
项目采用响应式设计，支持：
- 桌面端（>= 1024px）
- 平板端（768px - 1023px）
- 移动端（< 768px）

### 状态管理
使用 Pinia 进行状态管理，主要包括：
- 用户状态（登录信息、权限等）
- 图书状态（列表、搜索条件等）
- 全局加载状态

## 扩展功能

项目预留了扩展接口，可以轻松添加：
- 管理员后台功能
- 图书评价系统
- 消息通知功能
- 支付集成
- 地理位置服务
- 实时聊天功能

## 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 许可证

MIT License
