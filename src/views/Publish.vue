<template>
  <div class="publish">
    <AppHeader />
    
    <div class="container">
      <div class="publish-content">
        <div class="publish-header">
          <h2>发布图书</h2>
          <p>分享您的二手图书，让知识传递下去</p>
        </div>
        
        <el-form
          ref="publishFormRef"
          :model="publishForm"
          :rules="publishRules"
          label-width="100px"
          class="publish-form"
        >
          <el-row :gutter="40">
            <el-col :lg="12" :md="24">
              <el-form-item label="书名" prop="title">
                <el-input
                  v-model="publishForm.title"
                  placeholder="请输入图书名称"
                  maxlength="100"
                  show-word-limit
                />
              </el-form-item>
              
              <el-form-item label="作者" prop="author">
                <el-input
                  v-model="publishForm.author"
                  placeholder="请输入作者姓名"
                  maxlength="50"
                />
              </el-form-item>
              
              <el-form-item label="分类" prop="category">
                <el-select
                  v-model="publishForm.category"
                  placeholder="请选择图书分类"
                  style="width: 100%"
                >
                  <el-option
                    v-for="category in categories"
                    :key="category"
                    :label="category"
                    :value="category"
                  />
                </el-select>
              </el-form-item>
              
              <el-form-item label="成色" prop="condition">
                <el-radio-group v-model="publishForm.condition">
                  <el-radio label="new">全新</el-radio>
                  <el-radio label="good">良好</el-radio>
                  <el-radio label="fair">一般</el-radio>
                  <el-radio label="poor">较差</el-radio>
                </el-radio-group>
              </el-form-item>
              
              <el-form-item label="价格" prop="price">
                <el-input-number
                  v-model="publishForm.price"
                  :min="0.01"
                  :max="9999.99"
                  :precision="2"
                  :step="0.1"
                  style="width: 100%"
                />
                <span class="price-unit">元</span>
              </el-form-item>
            </el-col>
            
            <el-col :lg="12" :md="24">
              <el-form-item label="封面图片" prop="cover">
                <div class="cover-upload">
                  <div class="cover-preview" v-if="publishForm.cover">
                    <img :src="publishForm.cover" alt="封面预览" />
                    <div class="cover-actions">
                      <el-button size="small" @click="changeCover">更换</el-button>
                      <el-button size="small" type="danger" @click="removeCover">删除</el-button>
                    </div>
                  </div>
                  <div class="cover-placeholder" v-else @click="selectCover">
                    <el-icon size="48"><Plus /></el-icon>
                    <p>点击上传封面图片</p>
                  </div>
                  <input
                    ref="fileInputRef"
                    type="file"
                    accept="image/*"
                    style="display: none"
                    @change="handleFileChange"
                  />
                </div>
              </el-form-item>
              
              <el-form-item label="图书描述" prop="description">
                <el-input
                  v-model="publishForm.description"
                  type="textarea"
                  :rows="6"
                  placeholder="请描述图书的详细信息，如使用情况、购买时间等"
                  maxlength="500"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-form-item class="submit-section">
            <el-button type="primary" size="large" @click="handleSubmit" :loading="loading">
              发布图书
            </el-button>
            <el-button size="large" @click="handleReset">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElForm, ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { publishBook } from '@/api/books'
import { bookCategories } from '@/mock/data'
import AppHeader from '@/components/common/AppHeader.vue'
import type { PublishBookForm } from '@/types'
import router from '@/router'

const publishFormRef = ref<InstanceType<typeof ElForm>>()
const fileInputRef = ref<HTMLInputElement>()
const loading = ref(false)
const categories = ref<string[]>([])

// 表单数据
const publishForm = reactive<PublishBookForm>({
  title: '',
  author: '',
  description: '',
  price: 0,
  cover: '',
  condition: 'good',
  category: ''
})

// 表单验证规则
const publishRules = {
  title: [
    { required: true, message: '请输入图书名称', trigger: 'blur' },
    { min: 2, max: 100, message: '图书名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  author: [
    { required: true, message: '请输入作者姓名', trigger: 'blur' },
    { min: 1, max: 50, message: '作者姓名长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择图书分类', trigger: 'change' }
  ],
  condition: [
    { required: true, message: '请选择图书成色', trigger: 'change' }
  ],
  price: [
    { required: true, message: '请输入图书价格', trigger: 'blur' },
    { type: 'number', min: 0.01, max: 9999.99, message: '价格范围在 0.01 到 9999.99 元', trigger: 'blur' }
  ],
  cover: [
    { required: true, message: '请上传封面图片', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入图书描述', trigger: 'blur' },
    { min: 10, max: 500, message: '描述长度在 10 到 500 个字符', trigger: 'blur' }
  ]
}

// 选择封面
const selectCover = () => {
  fileInputRef.value?.click()
}

// 更换封面
const changeCover = () => {
  fileInputRef.value?.click()
}

// 删除封面
const removeCover = () => {
  publishForm.cover = ''
}

// 处理文件选择
const handleFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (!file) return

  // 检查文件类型
  if (!file.type.startsWith('image/')) {
    ElMessage.error('请选择图片文件')
    return
  }

  // 检查文件大小（限制为 5MB）
  if (file.size > 5 * 1024 * 1024) {
    ElMessage.error('图片大小不能超过 5MB')
    return
  }

  // 使用默认图片 URL（实际项目中应该上传到服务器）
  const defaultImages = [
    'https://img3.doubanio.com/view/subject/l/public/s1074291.jpg',
    'https://img9.doubanio.com/view/subject/l/public/s29563113.jpg',
    'https://img3.doubanio.com/view/subject/l/public/s4610502.jpg',
    'https://img1.doubanio.com/view/subject/l/public/s1074290.jpg',
    'https://img1.doubanio.com/view/subject/l/public/s28659699.jpg'
  ]

  // 随机选择一个默认图片
  publishForm.cover = defaultImages[Math.floor(Math.random() * defaultImages.length)]

  // 清空 input 值，允许重复选择同一文件
  target.value = ''
}

// 提交表单
const handleSubmit = async () => {
  if (!publishFormRef.value) return
  
  try {
    await publishFormRef.value.validate()
    
    loading.value = true
    await publishBook(publishForm)
    
    ElMessage.success('图书发布成功！')
    router.push('/my-books')
  } catch (error: any) {
    if (error.message) {
      ElMessage.error(error.message)
    }
  } finally {
    loading.value = false
  }
}

// 重置表单
const handleReset = () => {
  publishFormRef.value?.resetFields()
  publishForm.cover = ''
}

// 初始化
onMounted(() => {
  categories.value = bookCategories
})
</script>

<style scoped>
.publish {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 30px 20px;
}

.publish-content {
  background: white;
  border-radius: 8px;
  padding: 40px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.publish-header {
  text-align: center;
  margin-bottom: 40px;
}

.publish-header h2 {
  color: #333;
  margin-bottom: 8px;
  font-size: 28px;
  font-weight: 600;
}

.publish-header p {
  color: #666;
  font-size: 16px;
  margin: 0;
}

.publish-form {
  max-width: 100%;
}

.price-unit {
  margin-left: 8px;
  color: #666;
}

.cover-upload {
  width: 100%;
}

.cover-preview {
  position: relative;
  width: 200px;
  height: 280px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.cover-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  padding: 8px;
  display: flex;
  gap: 8px;
  justify-content: center;
}

.cover-placeholder {
  width: 200px;
  height: 280px;
  border: 2px dashed #ddd;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: border-color 0.3s;
  color: #999;
}

.cover-placeholder:hover {
  border-color: #409eff;
  color: #409eff;
}

.cover-placeholder p {
  margin: 8px 0 0 0;
  font-size: 14px;
}

.submit-section {
  text-align: center;
  margin-top: 40px;
}

.submit-section .el-button {
  min-width: 120px;
  margin: 0 10px;
}

@media (max-width: 768px) {
  .container {
    padding: 20px 15px;
  }
  
  .publish-content {
    padding: 30px 20px;
  }
  
  .publish-header h2 {
    font-size: 24px;
  }
  
  .cover-preview,
  .cover-placeholder {
    width: 160px;
    height: 224px;
  }
}
</style>
