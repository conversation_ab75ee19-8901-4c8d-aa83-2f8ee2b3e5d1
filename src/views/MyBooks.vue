<template>
  <div class="my-books">
    <AppHeader />
    
    <div class="container">
      <div class="page-header">
        <h2>我的发布</h2>
        <p>管理您发布的图书信息</p>
        <el-button type="primary" @click="$router.push('/publish')">
          发布新图书
        </el-button>
      </div>
      
      <div class="books-content">
        <el-table
          :data="booksStore.myBooks"
          v-loading="booksStore.loading"
          stripe
          style="width: 100%"
        >
          <el-table-column label="封面" width="80">
            <template #default="{ row }">
              <img :src="row.cover" :alt="row.title" class="book-cover-small" />
            </template>
          </el-table-column>
          
          <el-table-column prop="title" label="书名" min-width="150" />
          
          <el-table-column prop="author" label="作者" width="120" />
          
          <el-table-column prop="category" label="分类" width="100">
            <template #default="{ row }">
              <el-tag size="small" type="info">{{ row.category }}</el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="condition" label="成色" width="80">
            <template #default="{ row }">
              <el-tag size="small" :type="getConditionTagType(row.condition)">
                {{ getConditionText(row.condition) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="price" label="价格" width="100">
            <template #default="{ row }">
              <span class="price">¥{{ row.price.toFixed(2) }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag
                size="small"
                :type="row.status === 'available' ? 'success' : 'danger'"
              >
                {{ row.status === 'available' ? '在售' : '已售出' }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="createdAt" label="发布时间" width="120">
            <template #default="{ row }">
              {{ formatDate(row.createdAt) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button
                size="small"
                type="primary"
                link
                @click="viewBook(row)"
              >
                查看
              </el-button>
              <el-button
                v-if="row.status === 'available'"
                size="small"
                type="danger"
                link
                @click="removeBook(row)"
              >
                下架
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 空状态 -->
        <el-empty
          v-if="!booksStore.loading && booksStore.myBooks.length === 0"
          description="您还没有发布任何图书"
        >
          <el-button type="primary" @click="$router.push('/publish')">
            发布图书
          </el-button>
        </el-empty>
      </div>
    </div>
    
    <!-- 图书详情对话框 -->
    <el-dialog
      v-model="showBookDialog"
      title="图书详情"
      width="600px"
      :before-close="handleCloseDialog"
    >
      <div v-if="selectedBook" class="book-detail">
        <el-row :gutter="20">
          <el-col :span="8">
            <img :src="selectedBook.cover" :alt="selectedBook.title" class="book-cover-large" />
          </el-col>
          <el-col :span="16">
            <h3>{{ selectedBook.title }}</h3>
            <p><strong>作者：</strong>{{ selectedBook.author }}</p>
            <p><strong>分类：</strong>{{ selectedBook.category }}</p>
            <p><strong>成色：</strong>{{ getConditionText(selectedBook.condition) }}</p>
            <p><strong>价格：</strong>¥{{ selectedBook.price.toFixed(2) }}</p>
            <p><strong>状态：</strong>{{ selectedBook.status === 'available' ? '在售' : '已售出' }}</p>
            <p><strong>发布时间：</strong>{{ formatDate(selectedBook.createdAt) }}</p>
          </el-col>
        </el-row>
        <div class="book-description">
          <h4>图书描述</h4>
          <p>{{ selectedBook.description }}</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useBooksStore } from '@/stores/books'
import AppHeader from '@/components/common/AppHeader.vue'
import type { Book } from '@/types'

const booksStore = useBooksStore()
const showBookDialog = ref(false)
const selectedBook = ref<Book | null>(null)

// 获取成色文本
const getConditionText = (condition: string) => {
  const conditionMap = {
    new: '全新',
    good: '良好',
    fair: '一般',
    poor: '较差'
  }
  return conditionMap[condition as keyof typeof conditionMap] || '未知'
}

// 获取成色标签类型
const getConditionTagType = (condition: string) => {
  const typeMap = {
    new: 'success',
    good: 'primary',
    fair: 'warning',
    poor: 'danger'
  }
  return typeMap[condition as keyof typeof typeMap] || 'info'
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 查看图书详情
const viewBook = (book: Book) => {
  selectedBook.value = book
  showBookDialog.value = true
}

// 下架图书
const removeBook = async (book: Book) => {
  try {
    await ElMessageBox.confirm(
      `确定要下架《${book.title}》吗？`,
      '确认下架',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 更新图书状态（实际项目中应该调用 API）
    book.status = 'sold'
    ElMessage.success('图书已下架')
    
    // 刷新列表
    await booksStore.fetchMyBooks()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('下架失败')
    }
  }
}

// 关闭对话框
const handleCloseDialog = () => {
  showBookDialog.value = false
  selectedBook.value = null
}

// 初始化
onMounted(async () => {
  await booksStore.fetchMyBooks()
})
</script>

<style scoped>
.my-books {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 30px 20px;
}

.page-header {
  background: white;
  border-radius: 8px;
  padding: 30px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.page-header h2 {
  color: #333;
  margin: 0 0 5px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.books-content {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.book-cover-small {
  width: 50px;
  height: 70px;
  object-fit: cover;
  border-radius: 4px;
}

.book-cover-large {
  width: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.price {
  color: #e74c3c;
  font-weight: 600;
}

.book-detail h3 {
  color: #333;
  margin: 0 0 15px 0;
  font-size: 20px;
}

.book-detail p {
  margin: 8px 0;
  color: #666;
}

.book-description {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.book-description h4 {
  color: #333;
  margin: 0 0 10px 0;
  font-size: 16px;
}

.book-description p {
  line-height: 1.6;
  color: #666;
}

@media (max-width: 768px) {
  .container {
    padding: 20px 15px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .page-header h2 {
    font-size: 20px;
  }
  
  .books-content {
    padding: 15px;
  }
  
  :deep(.el-table) {
    font-size: 12px;
  }
}
</style>
