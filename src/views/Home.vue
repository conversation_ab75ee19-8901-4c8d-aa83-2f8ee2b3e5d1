<template>
  <div class="home">
    <!-- 顶部导航 -->
    <AppHeader />
    
    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="container">
        <div class="search-bar">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索图书名称、作者..."
            size="large"
            class="search-input"
            @keyup.enter="handleSearch"
          >
            <template #append>
              <el-button 
                type="primary" 
                icon="Search"
                @click="handleSearch"
              >
                搜索
              </el-button>
            </template>
          </el-input>
        </div>
      </div>
    </div>
    
    <!-- 分类筛选 -->
    <div class="category-section">
      <div class="container">
        <div class="category-tabs">
          <el-button
            v-for="category in booksStore.categories"
            :key="category"
            :type="booksStore.selectedCategory === category ? 'primary' : 'default'"
            size="small"
            @click="handleCategoryChange(category)"
          >
            {{ category }}
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 图书列表 -->
    <div class="books-section">
      <div class="container">
        <el-row :gutter="20" v-loading="booksStore.loading">
          <el-col
            v-for="book in booksStore.books"
            :key="book.id"
            :xs="12"
            :sm="8"
            :md="6"
            :lg="4"
            :xl="4"
            class="book-col"
          >
            <BookCard :book="book" @buy="handleBuyBook" />
          </el-col>
        </el-row>
        
        <!-- 空状态 -->
        <el-empty
          v-if="!booksStore.loading && booksStore.books.length === 0"
          description="暂无图书"
        />
        
        <!-- 分页 -->
        <div class="pagination" v-if="booksStore.total > 0">
          <el-pagination
            v-model:current-page="booksStore.currentPage"
            :page-size="booksStore.pageSize"
            :total="booksStore.total"
            layout="prev, pager, next, jumper"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useBooksStore } from '@/stores/books'
import { useUserStore } from '@/stores/user'
import { createOrder } from '@/api/orders'
import AppHeader from '@/components/common/AppHeader.vue'
import BookCard from '@/components/common/BookCard.vue'
import type { Book } from '@/types'

const booksStore = useBooksStore()
const userStore = useUserStore()
const searchKeyword = ref('')

// 搜索图书
const handleSearch = () => {
  booksStore.searchBooks(searchKeyword.value)
}

// 切换分类
const handleCategoryChange = (category: string) => {
  booksStore.changeCategory(category)
}

// 分页
const handlePageChange = (page: number) => {
  booksStore.changePage(page)
}

// 购买图书
const handleBuyBook = async (book: Book) => {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    return
  }
  
  if (book.sellerId === userStore.user?.id) {
    ElMessage.warning('不能购买自己发布的图书')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要购买《${book.title}》吗？价格：¥${book.price}`,
      '确认购买',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await createOrder(book.id)
    ElMessage.success('购买成功！')
    
    // 刷新图书列表
    booksStore.fetchBooks()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '购买失败')
    }
  }
}

// 初始化
onMounted(async () => {
  await booksStore.fetchCategories()
  await booksStore.fetchBooks()
})
</script>

<style scoped>
.home {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.search-section {
  background: white;
  padding: 30px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-bar {
  max-width: 600px;
  margin: 0 auto;
}

.search-input {
  width: 100%;
}

.category-section {
  background: white;
  padding: 20px 0;
  border-top: 1px solid #eee;
}

.category-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
}

.books-section {
  padding: 30px 0;
}

.book-col {
  margin-bottom: 20px;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}

@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }
  
  .search-section {
    padding: 20px 0;
  }
  
  .category-tabs {
    justify-content: flex-start;
  }
  
  .books-section {
    padding: 20px 0;
  }
}
</style>
