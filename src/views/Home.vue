<template>
  <div class="home">
    <!-- 顶部导航 -->
    <div class="header">
      <div class="container">
        <div class="header-content">
          <div class="logo">
            <el-icon><Reading /></el-icon>
            <span>校园二手书</span>
          </div>
          <div class="nav-actions">
            <el-button type="primary" @click="$router.push('/login')">
              登录
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="container">
        <div class="search-bar">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索图书名称、作者..."
            size="large"
            class="search-input"
            @keyup.enter="handleSearch"
          >
            <template #append>
              <el-button
                type="primary"
                @click="handleSearch"
              >
                搜索
              </el-button>
            </template>
          </el-input>
        </div>
      </div>
    </div>

    <!-- 分类筛选 -->
    <div class="category-section">
      <div class="container">
        <div class="category-tabs">
          <el-button
            v-for="category in categories"
            :key="category"
            :type="selectedCategory === category ? 'primary' : 'default'"
            size="small"
            @click="handleCategoryChange(category)"
          >
            {{ category }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- 图书列表 -->
    <div class="books-section">
      <div class="container">
        <el-row :gutter="30" v-loading="loading">
          <el-col
            v-for="book in displayBooks"
            :key="book.id"
            :xs="24"
            :sm="12"
            :md="12"
            :lg="8"
            :xl="6"
            class="book-col"
          >
            <div class="book-card">
              <div class="book-cover">
                <img :src="book.cover" :alt="book.title" />
                <div class="book-status" v-if="book.status === 'sold'">
                  <span>已售出</span>
                </div>
              </div>
              <div class="book-info">
                <h3 class="book-title">{{ book.title }}</h3>
                <p class="book-author">作者：{{ book.author }}</p>
                <div class="book-meta">
                  <el-tag size="small" type="info">{{ book.category }}</el-tag>
                </div>
                <div class="book-footer">
                  <div class="book-price">¥{{ book.price.toFixed(2) }}</div>
                  <el-button
                    type="primary"
                    @click="handleBuyBook(book)"
                    :disabled="book.status === 'sold'"
                  >
                    {{ book.status === 'sold' ? '已售出' : '立即购买' }}
                  </el-button>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 空状态 -->
        <el-empty
          v-if="!loading && displayBooks.length === 0"
          description="暂无图书"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Reading } from '@element-plus/icons-vue'
import { mockBooks, bookCategories } from '@/mock/data'
import type { Book } from '@/types'

const searchKeyword = ref('')
const selectedCategory = ref('全部')
const loading = ref(false)
const books = ref<Book[]>([])
const categories = ref<string[]>(['全部', ...bookCategories])

// 显示的图书列表
const displayBooks = computed(() => {
  let filteredBooks = books.value

  // 按分类筛选
  if (selectedCategory.value !== '全部') {
    filteredBooks = filteredBooks.filter(book => book.category === selectedCategory.value)
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filteredBooks = filteredBooks.filter(book =>
      book.title.toLowerCase().includes(keyword) ||
      book.author.toLowerCase().includes(keyword)
    )
  }

  return filteredBooks.filter(book => book.status === 'available')
})

// 搜索图书
const handleSearch = () => {
  // 搜索逻辑已在 computed 中处理
}

// 切换分类
const handleCategoryChange = (category: string) => {
  selectedCategory.value = category
}

// 购买图书
const handleBuyBook = async (book: Book) => {
  const token = localStorage.getItem('token')
  if (!token) {
    ElMessage.warning('请先登录')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要购买《${book.title}》吗？价格：¥${book.price}`,
      '确认购买',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 模拟购买
    book.status = 'sold'
    ElMessage.success('购买成功！')

  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '购买失败')
    }
  }
}

// 初始化
onMounted(() => {
  loading.value = true
  setTimeout(() => {
    books.value = [...mockBooks]
    loading.value = false
  }, 500)
})
</script>

<style scoped>
.home {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 40px;
}

.header {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #409eff;
  cursor: pointer;
}

.search-section {
  background: white;
  padding: 30px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-bar {
  max-width: 800px;
  margin: 0 auto;
}

.search-input {
  width: 100%;
}

.category-section {
  background: white;
  padding: 20px 0;
  border-top: 1px solid #eee;
}

.category-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
}

.books-section {
  padding: 30px 0;
}

.book-col {
  margin-bottom: 20px;
}

.book-card {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s, box-shadow 0.3s;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.book-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.book-cover {
  width: 100%;
  height: 350px;
  overflow: hidden;
  position: relative;
}

.book-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.book-card:hover .book-cover img {
  transform: scale(1.05);
}

.book-status {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1;
}

.book-info {
  padding: 24px;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 200px;
}

.book-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 12px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex: 1;
}

.book-author {
  font-size: 15px;
  color: #666;
  margin: 0 0 12px 0;
}

.book-meta {
  margin-bottom: 16px;
}

.book-footer {
  margin-top: auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}

.book-price {
  font-size: 22px;
  font-weight: 600;
  color: #e74c3c;
  flex-shrink: 0;
}

.book-footer .el-button {
  flex: 1;
  height: 44px;
  font-size: 15px;
  font-weight: 500;
  min-width: 100px;
}

@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }

  .search-section {
    padding: 20px 0;
  }

  .category-tabs {
    justify-content: flex-start;
  }

  .books-section {
    padding: 20px 0;
  }

  .book-cover {
    height: 240px;
  }

  .book-info {
    padding: 16px;
    min-height: 140px;
  }

  .book-title {
    font-size: 14px;
  }

  .book-price {
    font-size: 16px;
  }

  .book-footer .el-button {
    height: 32px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .book-cover {
    height: 200px;
  }

  .book-info {
    padding: 12px;
    min-height: 120px;
  }
}
</style>
