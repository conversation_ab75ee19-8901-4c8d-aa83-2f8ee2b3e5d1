<template>
  <div class="orders">
    <AppHeader />
    
    <div class="container">
      <div class="page-header">
        <h2>我的订单</h2>
        <p>查看您的购买记录</p>
      </div>
      
      <div class="orders-content">
        <el-tabs v-model="activeTab" @tab-change="handleTabChange">
          <el-tab-pane label="我的购买" name="purchases">
            <el-table
              :data="purchaseOrders"
              v-loading="loading"
              stripe
              style="width: 100%"
            >
              <el-table-column label="图书信息" min-width="200">
                <template #default="{ row }">
                  <div class="book-info">
                    <img :src="row.book.cover" :alt="row.book.title" class="book-cover-small" />
                    <div class="book-details">
                      <h4>{{ row.book.title }}</h4>
                      <p>{{ row.book.author }}</p>
                    </div>
                  </div>
                </template>
              </el-table-column>
              
              <el-table-column prop="sellerName" label="卖家" width="120" />
              
              <el-table-column prop="totalPrice" label="价格" width="100">
                <template #default="{ row }">
                  <span class="price">¥{{ row.totalPrice.toFixed(2) }}</span>
                </template>
              </el-table-column>
              
              <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                  <el-tag
                    size="small"
                    :type="getOrderStatusType(row.status)"
                  >
                    {{ getOrderStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              
              <el-table-column prop="createdAt" label="购买时间" width="120">
                <template #default="{ row }">
                  {{ formatDate(row.createdAt) }}
                </template>
              </el-table-column>
              
              <el-table-column label="操作" width="100">
                <template #default="{ row }">
                  <el-button
                    size="small"
                    type="primary"
                    link
                    @click="viewOrder(row)"
                  >
                    查看详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            
            <!-- 空状态 -->
            <el-empty
              v-if="!loading && purchaseOrders.length === 0"
              description="您还没有购买任何图书"
            >
              <el-button type="primary" @click="$router.push('/')">
                去购买
              </el-button>
            </el-empty>
          </el-tab-pane>
          
          <el-tab-pane label="我的销售" name="sales">
            <el-table
              :data="salesOrders"
              v-loading="loading"
              stripe
              style="width: 100%"
            >
              <el-table-column label="图书信息" min-width="200">
                <template #default="{ row }">
                  <div class="book-info">
                    <img :src="row.book.cover" :alt="row.book.title" class="book-cover-small" />
                    <div class="book-details">
                      <h4>{{ row.book.title }}</h4>
                      <p>{{ row.book.author }}</p>
                    </div>
                  </div>
                </template>
              </el-table-column>
              
              <el-table-column prop="buyerName" label="买家" width="120" />
              
              <el-table-column prop="totalPrice" label="价格" width="100">
                <template #default="{ row }">
                  <span class="price">¥{{ row.totalPrice.toFixed(2) }}</span>
                </template>
              </el-table-column>
              
              <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                  <el-tag
                    size="small"
                    :type="getOrderStatusType(row.status)"
                  >
                    {{ getOrderStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              
              <el-table-column prop="createdAt" label="销售时间" width="120">
                <template #default="{ row }">
                  {{ formatDate(row.createdAt) }}
                </template>
              </el-table-column>
              
              <el-table-column label="操作" width="100">
                <template #default="{ row }">
                  <el-button
                    size="small"
                    type="primary"
                    link
                    @click="viewOrder(row)"
                  >
                    查看详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            
            <!-- 空状态 -->
            <el-empty
              v-if="!loading && salesOrders.length === 0"
              description="您还没有销售任何图书"
            >
              <el-button type="primary" @click="$router.push('/publish')">
                发布图书
              </el-button>
            </el-empty>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    
    <!-- 订单详情对话框 -->
    <el-dialog
      v-model="showOrderDialog"
      title="订单详情"
      width="600px"
      :before-close="handleCloseDialog"
    >
      <div v-if="selectedOrder" class="order-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单号">{{ selectedOrder.id }}</el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="getOrderStatusType(selectedOrder.status)">
              {{ getOrderStatusText(selectedOrder.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="图书名称">{{ selectedOrder.book.title }}</el-descriptions-item>
          <el-descriptions-item label="作者">{{ selectedOrder.book.author }}</el-descriptions-item>
          <el-descriptions-item label="买家">{{ selectedOrder.buyerName }}</el-descriptions-item>
          <el-descriptions-item label="卖家">{{ selectedOrder.sellerName }}</el-descriptions-item>
          <el-descriptions-item label="数量">{{ selectedOrder.quantity }}</el-descriptions-item>
          <el-descriptions-item label="总价">¥{{ selectedOrder.totalPrice.toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">
            {{ formatDateTime(selectedOrder.createdAt) }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="book-preview">
          <h4>图书信息</h4>
          <div class="book-info-large">
            <img :src="selectedOrder.book.cover" :alt="selectedOrder.book.title" class="book-cover-medium" />
            <div class="book-details-large">
              <h3>{{ selectedOrder.book.title }}</h3>
              <p><strong>作者：</strong>{{ selectedOrder.book.author }}</p>
              <p><strong>分类：</strong>{{ selectedOrder.book.category }}</p>
              <p><strong>描述：</strong>{{ selectedOrder.book.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getMyOrders, getMySales } from '@/api/orders'
import AppHeader from '@/components/common/AppHeader.vue'
import type { Order } from '@/types'

const activeTab = ref('purchases')
const loading = ref(false)
const purchaseOrders = ref<Order[]>([])
const salesOrders = ref<Order[]>([])
const showOrderDialog = ref(false)
const selectedOrder = ref<Order | null>(null)

// 获取订单状态文本
const getOrderStatusText = (status: string) => {
  const statusMap = {
    pending: '待确认',
    confirmed: '已确认',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status as keyof typeof statusMap] || '未知'
}

// 获取订单状态标签类型
const getOrderStatusType = (status: string) => {
  const typeMap = {
    pending: 'warning',
    confirmed: 'primary',
    completed: 'success',
    cancelled: 'danger'
  }
  return typeMap[status as keyof typeof typeMap] || 'info'
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

// 切换标签页
const handleTabChange = (tabName: string) => {
  if (tabName === 'purchases' && purchaseOrders.value.length === 0) {
    fetchPurchaseOrders()
  } else if (tabName === 'sales' && salesOrders.value.length === 0) {
    fetchSalesOrders()
  }
}

// 获取购买订单
const fetchPurchaseOrders = async () => {
  try {
    loading.value = true
    const response = await getMyOrders({ page: 1, pageSize: 50 })
    purchaseOrders.value = response.list
  } catch (error) {
    console.error('获取购买订单失败:', error)
  } finally {
    loading.value = false
  }
}

// 获取销售订单
const fetchSalesOrders = async () => {
  try {
    loading.value = true
    const response = await getMySales({ page: 1, pageSize: 50 })
    salesOrders.value = response.list
  } catch (error) {
    console.error('获取销售订单失败:', error)
  } finally {
    loading.value = false
  }
}

// 查看订单详情
const viewOrder = (order: Order) => {
  selectedOrder.value = order
  showOrderDialog.value = true
}

// 关闭对话框
const handleCloseDialog = () => {
  showOrderDialog.value = false
  selectedOrder.value = null
}

// 初始化
onMounted(async () => {
  await fetchPurchaseOrders()
})
</script>

<style scoped>
.orders {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 30px 20px;
}

.page-header {
  background: white;
  border-radius: 8px;
  padding: 30px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header h2 {
  color: #333;
  margin: 0 0 5px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.orders-content {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.book-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.book-cover-small {
  width: 50px;
  height: 70px;
  object-fit: cover;
  border-radius: 4px;
  flex-shrink: 0;
}

.book-cover-medium {
  width: 120px;
  height: 168px;
  object-fit: cover;
  border-radius: 8px;
  flex-shrink: 0;
}

.book-details h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #333;
  font-weight: 600;
}

.book-details p {
  margin: 0;
  font-size: 12px;
  color: #666;
}

.price {
  color: #e74c3c;
  font-weight: 600;
}

.order-detail {
  padding: 10px 0;
}

.book-preview {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.book-preview h4 {
  color: #333;
  margin: 0 0 15px 0;
  font-size: 16px;
}

.book-info-large {
  display: flex;
  gap: 20px;
}

.book-details-large h3 {
  color: #333;
  margin: 0 0 10px 0;
  font-size: 18px;
}

.book-details-large p {
  margin: 6px 0;
  color: #666;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .container {
    padding: 20px 15px;
  }
  
  .page-header {
    padding: 20px;
  }
  
  .page-header h2 {
    font-size: 20px;
  }
  
  .orders-content {
    padding: 15px;
  }
  
  .book-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .book-info-large {
    flex-direction: column;
  }
  
  :deep(.el-table) {
    font-size: 12px;
  }
}
</style>
