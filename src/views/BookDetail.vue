<template>
  <div class="book-detail">
    <AppHeader />
    
    <div class="container" v-loading="loading">
      <div v-if="book" class="detail-content">
        <el-row :gutter="40">
          <el-col :lg="8" :md="10" :sm="24">
            <div class="book-cover">
              <img :src="book.cover" :alt="book.title" />
              <div class="book-status" v-if="book.status === 'sold'">
                <span>已售出</span>
              </div>
            </div>
          </el-col>
          
          <el-col :lg="16" :md="14" :sm="24">
            <div class="book-info">
              <h1 class="book-title">{{ book.title }}</h1>
              
              <div class="book-meta">
                <div class="meta-item">
                  <span class="label">作者：</span>
                  <span class="value">{{ book.author }}</span>
                </div>
                <div class="meta-item">
                  <span class="label">分类：</span>
                  <el-tag type="info">{{ book.category }}</el-tag>
                </div>
                <div class="meta-item">
                  <span class="label">成色：</span>
                  <el-tag :type="conditionTagType">{{ conditionText }}</el-tag>
                </div>
                <div class="meta-item">
                  <span class="label">卖家：</span>
                  <span class="value seller">{{ book.sellerName }}</span>
                </div>
                <div class="meta-item">
                  <span class="label">发布时间：</span>
                  <span class="value">{{ formatDate(book.createdAt) }}</span>
                </div>
              </div>
              
              <div class="price-section">
                <div class="price">
                  <span class="currency">¥</span>
                  <span class="amount">{{ book.price.toFixed(2) }}</span>
                </div>
                <div class="stock-info">
                  <span v-if="book.status === 'available'">现货 {{ book.stock }} 件</span>
                  <span v-else class="sold-out">已售出</span>
                </div>
              </div>
              
              <div class="actions">
                <el-button
                  v-if="book.status === 'available' && !isMyBook"
                  type="primary"
                  size="large"
                  @click="handleBuy"
                  :loading="buyLoading"
                >
                  立即购买
                </el-button>
                <el-button
                  v-else-if="book.status === 'sold'"
                  size="large"
                  disabled
                >
                  已售出
                </el-button>
                <el-button
                  v-else-if="isMyBook"
                  size="large"
                  disabled
                >
                  这是您发布的图书
                </el-button>
                
                <el-button size="large" @click="goBack">
                  返回
                </el-button>
              </div>
            </div>
          </el-col>
        </el-row>
        
        <div class="book-description">
          <h3>图书描述</h3>
          <p>{{ book.description }}</p>
        </div>
      </div>
      
      <el-empty v-else-if="!loading" description="图书不存在" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getBookDetail } from '@/api/books'
import { createOrder } from '@/api/orders'
import { useUserStore } from '@/stores/user'
import AppHeader from '@/components/common/AppHeader.vue'
import type { Book } from '@/types'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const book = ref<Book | null>(null)
const loading = ref(false)
const buyLoading = ref(false)

// 计算属性
const conditionText = computed(() => {
  if (!book.value) return ''
  const conditionMap = {
    new: '全新',
    good: '良好',
    fair: '一般',
    poor: '较差'
  }
  return conditionMap[book.value.condition] || '未知'
})

const conditionTagType = computed(() => {
  if (!book.value) return 'info'
  const typeMap = {
    new: 'success',
    good: 'primary',
    fair: 'warning',
    poor: 'danger'
  }
  return typeMap[book.value.condition] || 'info'
})

const isMyBook = computed(() => {
  return book.value?.sellerId === userStore.user?.id
})

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 购买图书
const handleBuy = async () => {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }
  
  if (!book.value) return
  
  try {
    await ElMessageBox.confirm(
      `确定要购买《${book.value.title}》吗？价格：¥${book.value.price}`,
      '确认购买',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    buyLoading.value = true
    await createOrder(book.value.id)
    
    ElMessage.success('购买成功！')
    
    // 更新图书状态
    book.value.status = 'sold'
    book.value.stock = 0
    
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '购买失败')
    }
  } finally {
    buyLoading.value = false
  }
}

// 返回上页
const goBack = () => {
  router.go(-1)
}

// 获取图书详情
const fetchBookDetail = async () => {
  const bookId = parseInt(route.params.id as string)
  
  if (isNaN(bookId)) {
    router.push('/404')
    return
  }
  
  try {
    loading.value = true
    book.value = await getBookDetail(bookId)
  } catch (error: any) {
    ElMessage.error(error.message || '获取图书详情失败')
    router.push('/404')
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  fetchBookDetail()
})
</script>

<style scoped>
.book-detail {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 30px 20px;
}

.detail-content {
  background: white;
  border-radius: 8px;
  padding: 40px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.book-cover {
  position: relative;
  text-align: center;
}

.book-cover img {
  width: 100%;
  max-width: 300px;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.book-status {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
}

.book-info {
  padding-left: 0;
}

.book-title {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  margin: 0 0 30px 0;
  line-height: 1.3;
}

.book-meta {
  margin-bottom: 30px;
}

.meta-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
}

.value {
  color: #333;
}

.seller {
  color: #409eff;
  font-weight: 500;
}

.price-section {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.price {
  display: flex;
  align-items: baseline;
  color: #e74c3c;
  font-weight: 600;
  margin-bottom: 8px;
}

.currency {
  font-size: 20px;
}

.amount {
  font-size: 36px;
}

.stock-info {
  color: #666;
  font-size: 14px;
}

.sold-out {
  color: #e74c3c;
  font-weight: 500;
}

.actions {
  display: flex;
  gap: 16px;
  margin-bottom: 40px;
}

.book-description {
  border-top: 1px solid #eee;
  padding-top: 30px;
}

.book-description h3 {
  color: #333;
  margin: 0 0 20px 0;
  font-size: 20px;
  font-weight: 600;
}

.book-description p {
  color: #666;
  line-height: 1.8;
  font-size: 16px;
  margin: 0;
}

@media (max-width: 768px) {
  .container {
    padding: 20px 15px;
  }
  
  .detail-content {
    padding: 30px 20px;
  }
  
  .book-title {
    font-size: 24px;
    margin-bottom: 20px;
  }
  
  .book-info {
    margin-top: 30px;
  }
  
  .price .amount {
    font-size: 28px;
  }
  
  .actions {
    flex-direction: column;
  }
  
  .meta-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .label {
    min-width: auto;
  }
}
</style>
