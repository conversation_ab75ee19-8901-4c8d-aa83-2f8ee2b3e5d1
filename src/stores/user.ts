import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, LoginForm, RegisterForm } from '@/types'
import { login, register, getUserInfo } from '@/api/auth'
import { ElMessage } from 'element-plus'
import router from '@/router'

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string>('')
  const loading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const isAdmin = computed(() => user.value?.role === 'admin')

  // 初始化用户信息
  const initUserInfo = () => {
    const savedToken = localStorage.getItem('token')
    const savedUser = localStorage.getItem('user')
    
    if (savedToken && savedUser) {
      token.value = savedToken
      user.value = JSON.parse(savedUser)
    }
  }

  // 登录
  const userLogin = async (loginForm: LoginForm) => {
    try {
      loading.value = true
      const response = await login(loginForm)
      
      // 保存用户信息和 token
      user.value = response.user
      token.value = response.token
      
      // 持久化存储
      localStorage.setItem('token', response.token)
      localStorage.setItem('user', JSON.stringify(response.user))
      localStorage.setItem('username', response.user.username)
      
      ElMessage.success('登录成功')
      
      // 跳转到首页
      router.push('/')
      
      return response
    } catch (error: any) {
      ElMessage.error(error.message || '登录失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 注册
  const userRegister = async (registerForm: RegisterForm) => {
    try {
      loading.value = true
      const response = await register(registerForm)
      
      // 保存用户信息和 token
      user.value = response.user
      token.value = response.token
      
      // 持久化存储
      localStorage.setItem('token', response.token)
      localStorage.setItem('user', JSON.stringify(response.user))
      localStorage.setItem('username', response.user.username)
      
      ElMessage.success('注册成功')
      
      // 跳转到首页
      router.push('/')
      
      return response
    } catch (error: any) {
      ElMessage.error(error.message || '注册失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取用户信息
  const fetchUserInfo = async () => {
    try {
      const userInfo = await getUserInfo()
      user.value = userInfo
      localStorage.setItem('user', JSON.stringify(userInfo))
      return userInfo
    } catch (error: any) {
      ElMessage.error(error.message || '获取用户信息失败')
      logout()
      throw error
    }
  }

  // 登出
  const logout = () => {
    user.value = null
    token.value = ''
    
    // 清除本地存储
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    localStorage.removeItem('username')
    
    // 跳转到登录页
    router.push('/login')
  }

  // 初始化
  initUserInfo()

  return {
    user,
    token,
    loading,
    isLoggedIn,
    isAdmin,
    userLogin,
    userRegister,
    fetchUserInfo,
    logout
  }
})
