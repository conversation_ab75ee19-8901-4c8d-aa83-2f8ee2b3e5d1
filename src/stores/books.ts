import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { Book, PaginationParams, PaginationResponse } from '@/types'
import { getBooks, getBookCategories, getMyBooks } from '@/api/books'
import { ElMessage } from 'element-plus'

export const useBooksStore = defineStore('books', () => {
  // 状态
  const books = ref<Book[]>([])
  const myBooks = ref<Book[]>([])
  const categories = ref<string[]>([])
  const loading = ref(false)
  const total = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(12)

  // 搜索条件
  const searchKeyword = ref('')
  const selectedCategory = ref('全部')

  // 获取图书列表
  const fetchBooks = async (params?: Partial<PaginationParams & {
    category?: string
    keyword?: string
  }>) => {
    try {
      loading.value = true
      
      const requestParams = {
        page: params?.page || currentPage.value,
        pageSize: params?.pageSize || pageSize.value,
        category: params?.category || selectedCategory.value,
        keyword: params?.keyword || searchKeyword.value
      }
      
      const response: PaginationResponse<Book> = await getBooks(requestParams)
      
      books.value = response.list
      total.value = response.total
      currentPage.value = response.page
      
      return response
    } catch (error: any) {
      ElMessage.error(error.message || '获取图书列表失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取我发布的图书
  const fetchMyBooks = async (params?: Partial<PaginationParams>) => {
    try {
      loading.value = true
      
      const requestParams = {
        page: params?.page || 1,
        pageSize: params?.pageSize || 10
      }
      
      const response: PaginationResponse<Book> = await getMyBooks(requestParams)
      myBooks.value = response.list
      
      return response
    } catch (error: any) {
      ElMessage.error(error.message || '获取我的图书失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取图书分类
  const fetchCategories = async () => {
    try {
      const response = await getBookCategories()
      categories.value = response
      return response
    } catch (error: any) {
      ElMessage.error(error.message || '获取分类失败')
      throw error
    }
  }

  // 搜索图书
  const searchBooks = async (keyword: string, category?: string) => {
    searchKeyword.value = keyword
    if (category !== undefined) {
      selectedCategory.value = category
    }
    currentPage.value = 1
    
    return await fetchBooks({
      page: 1,
      keyword,
      category: selectedCategory.value
    })
  }

  // 切换分类
  const changeCategory = async (category: string) => {
    selectedCategory.value = category
    currentPage.value = 1
    
    return await fetchBooks({
      page: 1,
      category
    })
  }

  // 分页
  const changePage = async (page: number) => {
    currentPage.value = page
    return await fetchBooks({ page })
  }

  // 重置搜索条件
  const resetSearch = () => {
    searchKeyword.value = ''
    selectedCategory.value = '全部'
    currentPage.value = 1
  }

  return {
    books,
    myBooks,
    categories,
    loading,
    total,
    currentPage,
    pageSize,
    searchKeyword,
    selectedCategory,
    fetchBooks,
    fetchMyBooks,
    fetchCategories,
    searchBooks,
    changeCategory,
    changePage,
    resetSearch
  }
})
