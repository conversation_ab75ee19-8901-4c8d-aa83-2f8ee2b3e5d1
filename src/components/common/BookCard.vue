<template>
  <div class="book-card">
    <div class="book-cover">
      <img :src="book.cover" :alt="book.title" @error="handleImageError" />
      <div class="book-status" v-if="book.status === 'sold'">
        <span>已售出</span>
      </div>
    </div>
    
    <div class="book-info">
      <h3 class="book-title" :title="book.title">{{ book.title }}</h3>
      <p class="book-author">{{ book.author }}</p>
      <p class="book-description">{{ book.description }}</p>
      
      <div class="book-meta">
        <el-tag size="small" type="info">{{ book.category }}</el-tag>
        <el-tag size="small" :type="conditionTagType">{{ conditionText }}</el-tag>
      </div>
      
      <div class="book-footer">
        <div class="price">
          <span class="currency">¥</span>
          <span class="amount">{{ book.price.toFixed(2) }}</span>
        </div>
        
        <div class="actions">
          <el-button
            v-if="book.status === 'available'"
            type="primary"
            size="small"
            @click="handleBuy"
          >
            购买
          </el-button>
          <el-button
            v-else
            size="small"
            disabled
          >
            已售出
          </el-button>
        </div>
      </div>
      
      <div class="seller-info">
        <span class="seller-label">卖家：</span>
        <span class="seller-name">{{ book.sellerName }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { Book } from '@/types'

interface Props {
  book: Book
}

interface Emits {
  (e: 'buy', book: Book): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 图书状态文本
const conditionText = computed(() => {
  const conditionMap = {
    new: '全新',
    good: '良好',
    fair: '一般',
    poor: '较差'
  }
  return conditionMap[props.book.condition] || '未知'
})

// 状态标签类型
const conditionTagType = computed(() => {
  const typeMap = {
    new: 'success',
    good: 'primary',
    fair: 'warning',
    poor: 'danger'
  }
  return typeMap[props.book.condition] || 'info'
})

// 处理购买
const handleBuy = () => {
  emit('buy', props.book)
}

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = 'https://via.placeholder.com/200x280?text=No+Image'
}
</script>

<style scoped>
.book-card {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s, box-shadow 0.3s;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.book-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.book-cover {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.book-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.book-card:hover .book-cover img {
  transform: scale(1.05);
}

.book-status {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.book-info {
  padding: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.book-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.book-author {
  font-size: 14px;
  color: #666;
  margin: 0 0 8px 0;
}

.book-description {
  font-size: 12px;
  color: #999;
  margin: 0 0 12px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex: 1;
}

.book-meta {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.book-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.price {
  display: flex;
  align-items: baseline;
  color: #e74c3c;
  font-weight: 600;
}

.currency {
  font-size: 14px;
}

.amount {
  font-size: 18px;
}

.seller-info {
  font-size: 12px;
  color: #999;
  border-top: 1px solid #eee;
  padding-top: 8px;
}

.seller-label {
  color: #666;
}

.seller-name {
  color: #409eff;
  font-weight: 500;
}

@media (max-width: 768px) {
  .book-cover {
    height: 160px;
  }
  
  .book-info {
    padding: 12px;
  }
  
  .book-title {
    font-size: 14px;
  }
  
  .price .amount {
    font-size: 16px;
  }
}
</style>
