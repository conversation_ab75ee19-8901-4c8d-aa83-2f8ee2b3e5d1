<template>
  <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm sticky-top">
    <div class="container-fluid px-4">
      <!-- Logo -->
      <router-link to="/" class="navbar-brand d-flex align-items-center text-decoration-none">
        <el-icon class="me-2 text-primary" size="28"><Reading /></el-icon>
        <span class="fw-bold text-primary fs-4">校园二手书</span>
      </router-link>

      <!-- 移动端切换按钮 -->
      <button
        class="navbar-toggler"
        type="button"
        data-bs-toggle="collapse"
        data-bs-target="#navbarNav"
        aria-controls="navbarNav"
        aria-expanded="false"
        aria-label="Toggle navigation"
      >
        <span class="navbar-toggler-icon"></span>
      </button>

      <!-- 导航菜单 -->
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
          <li class="nav-item">
            <router-link to="/" class="nav-link">首页</router-link>
          </li>
          <li class="nav-item" v-if="userStore.isLoggedIn">
            <router-link to="/publish" class="nav-link">发布图书</router-link>
          </li>
          <li class="nav-item" v-if="userStore.isLoggedIn">
            <router-link to="/my-books" class="nav-link">我的发布</router-link>
          </li>
          <li class="nav-item" v-if="userStore.isLoggedIn">
            <router-link to="/orders" class="nav-link">我的订单</router-link>
          </li>
        </ul>

        <!-- 用户信息 -->
        <div class="navbar-nav">
          <template v-if="userStore.isLoggedIn">
            <el-dropdown @command="handleCommand" class="d-flex align-items-center">
              <div class="d-flex align-items-center user-info px-3 py-2 rounded">
                <el-avatar :src="userStore.user?.avatar" size="small" class="me-2" />
                <span class="me-2">{{ userStore.user?.username }}</span>
                <el-icon><ArrowDown /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">个人信息</el-dropdown-item>
                  <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
          <template v-else>
            <div class="d-flex gap-2">
              <el-button type="primary" @click="$router.push('/login')">
                登录
              </el-button>
              <el-button @click="$router.push('/register')">
                注册
              </el-button>
            </div>
          </template>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { Reading, ArrowDown } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

// 处理下拉菜单命令
const handleCommand = (command: string) => {
  switch (command) {
    case 'profile':
      // TODO: 跳转到个人信息页面
      break
    case 'logout':
      handleLogout()
      break
  }
}

// 退出登录
const handleLogout = () => {
  userStore.logout()
}
</script>

<style scoped>
/* 导航链接样式 */
.nav-link {
  font-weight: 500;
  color: #333 !important;
  transition: color 0.3s ease;
}

.nav-link:hover,
.nav-link.router-link-active {
  color: #0d6efd !important;
}

/* 品牌链接样式 */
.navbar-brand:hover {
  text-decoration: none;
}

/* 用户信息区域 */
.user-info {
  cursor: pointer;
  transition: background-color 0.3s ease;
  border: 1px solid transparent;
}

.user-info:hover {
  background-color: #f8f9fa;
  border-color: #dee2e6;
}

/* 响应式调整 */
@media (max-width: 991.98px) {
  .navbar-nav {
    padding-top: 1rem;
  }

  .navbar-nav .nav-item {
    margin-bottom: 0.5rem;
  }
}

/* Element Plus 按钮在导航栏中的样式调整 */
:deep(.el-button) {
  border-radius: 0.375rem;
}

:deep(.el-dropdown) {
  height: auto;
}
</style>
