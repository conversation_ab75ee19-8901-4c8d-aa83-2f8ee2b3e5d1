<template>
  <header class="app-header">
    <div class="container">
      <div class="header-content">
        <!-- Logo -->
        <div class="logo" @click="$router.push('/')">
          <el-icon size="24"><Reading /></el-icon>
          <span>校园二手书</span>
        </div>
        
        <!-- 导航菜单 -->
        <nav class="nav-menu" v-if="!isMobile">
          <router-link to="/" class="nav-item">首页</router-link>
          <router-link v-if="userStore.isLoggedIn" to="/publish" class="nav-item">
            发布图书
          </router-link>
          <router-link v-if="userStore.isLoggedIn" to="/my-books" class="nav-item">
            我的发布
          </router-link>
          <router-link v-if="userStore.isLoggedIn" to="/orders" class="nav-item">
            我的订单
          </router-link>
        </nav>
        
        <!-- 用户信息 -->
        <div class="user-section">
          <template v-if="userStore.isLoggedIn">
            <el-dropdown @command="handleCommand">
              <div class="user-info">
                <el-avatar :src="userStore.user?.avatar" size="small" />
                <span class="username">{{ userStore.user?.username }}</span>
                <el-icon><ArrowDown /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">个人信息</el-dropdown-item>
                  <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
          <template v-else>
            <el-button type="primary" @click="$router.push('/login')">
              登录
            </el-button>
          </template>
        </div>
        
        <!-- 移动端菜单按钮 -->
        <el-button
          v-if="isMobile"
          type="text"
          @click="showMobileMenu = true"
          class="mobile-menu-btn"
        >
          <el-icon size="20"><Menu /></el-icon>
        </el-button>
      </div>
    </div>
    
    <!-- 移动端抽屉菜单 -->
    <el-drawer
      v-model="showMobileMenu"
      title="菜单"
      direction="rtl"
      size="250px"
    >
      <div class="mobile-nav">
        <router-link to="/" class="mobile-nav-item" @click="showMobileMenu = false">
          首页
        </router-link>
        <template v-if="userStore.isLoggedIn">
          <router-link to="/publish" class="mobile-nav-item" @click="showMobileMenu = false">
            发布图书
          </router-link>
          <router-link to="/my-books" class="mobile-nav-item" @click="showMobileMenu = false">
            我的发布
          </router-link>
          <router-link to="/orders" class="mobile-nav-item" @click="showMobileMenu = false">
            我的订单
          </router-link>
          <div class="mobile-nav-item" @click="handleLogout">
            退出登录
          </div>
        </template>
        <template v-else>
          <router-link to="/login" class="mobile-nav-item" @click="showMobileMenu = false">
            登录
          </router-link>
          <router-link to="/register" class="mobile-nav-item" @click="showMobileMenu = false">
            注册
          </router-link>
        </template>
      </div>
    </el-drawer>
  </header>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Reading, ArrowDown, Menu } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const showMobileMenu = ref(false)
const windowWidth = ref(window.innerWidth)

// 是否为移动端
const isMobile = computed(() => windowWidth.value < 768)

// 处理下拉菜单命令
const handleCommand = (command: string) => {
  switch (command) {
    case 'profile':
      // TODO: 跳转到个人信息页面
      break
    case 'logout':
      handleLogout()
      break
  }
}

// 退出登录
const handleLogout = () => {
  userStore.logout()
  showMobileMenu.value = false
}

// 监听窗口大小变化
const handleResize = () => {
  windowWidth.value = window.innerWidth
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.app-header {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #409eff;
  cursor: pointer;
  user-select: none;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 30px;
}

.nav-item {
  color: #333;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s;
}

.nav-item:hover,
.nav-item.router-link-active {
  color: #409eff;
}

.user-section {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f5f5;
}

.username {
  font-weight: 500;
  color: #333;
}

.mobile-menu-btn {
  padding: 8px;
}

.mobile-nav {
  display: flex;
  flex-direction: column;
}

.mobile-nav-item {
  padding: 15px 0;
  color: #333;
  text-decoration: none;
  border-bottom: 1px solid #eee;
  font-weight: 500;
}

.mobile-nav-item:hover {
  color: #409eff;
}

@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }
  
  .logo span {
    font-size: 16px;
  }
}
</style>
