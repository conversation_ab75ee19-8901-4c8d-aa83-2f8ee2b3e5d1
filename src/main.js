import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import router from './router'
import pinia from './stores'
import './assets/main.css'
import App from './App.vue'

const app = createApp(App)

// 先注册 Pinia
app.use(pinia)

// 注册 Element Plus 图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 注册 Element Plus
app.use(ElementPlus)

// 最后注册路由
app.use(router)

app.mount('#app')
