import type { User, Book, Order } from '@/types'

// 模拟用户数据
export const mockUsers: User[] = [
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    role: 'admin',
    createdAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 2,
    username: 'student1',
    email: '<EMAIL>',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    role: 'user',
    createdAt: '2024-01-02T00:00:00Z'
  },
  {
    id: 3,
    username: 'student2',
    email: '<EMAIL>',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    role: 'user',
    createdAt: '2024-01-03T00:00:00Z'
  }
]

// 模拟图书数据
export const mockBooks: Book[] = [
  {
    id: 1,
    title: '高等数学（上册）',
    author: '同济大学数学系',
    description: '经典的高等数学教材，适合理工科学生使用。书本保存完好，有少量笔记。',
    price: 25.00,
    cover: 'https://img3.doubanio.com/view/subject/l/public/s1074291.jpg',
    condition: 'good',
    category: '数学',
    sellerId: 2,
    sellerName: 'student1',
    status: 'available',
    stock: 1,
    createdAt: '2024-01-10T00:00:00Z',
    updatedAt: '2024-01-10T00:00:00Z'
  },
  {
    id: 2,
    title: 'Java核心技术 卷I',
    author: 'Cay S. Horstmann',
    description: 'Java编程经典教材，内容详实，适合初学者和进阶者。九成新，无破损。',
    price: 45.00,
    cover: 'https://img9.doubanio.com/view/subject/l/public/s29563113.jpg',
    condition: 'new',
    category: '计算机',
    sellerId: 3,
    sellerName: 'student2',
    status: 'available',
    stock: 1,
    createdAt: '2024-01-11T00:00:00Z',
    updatedAt: '2024-01-11T00:00:00Z'
  },
  {
    id: 3,
    title: '大学英语综合教程1',
    author: '李荫华',
    description: '大学英语必修教材，适合英语基础学习。有部分标记和笔记。',
    price: 18.00,
    cover: 'https://img3.doubanio.com/view/subject/l/public/s4610502.jpg',
    condition: 'fair',
    category: '英语',
    sellerId: 2,
    sellerName: 'student1',
    status: 'available',
    stock: 1,
    createdAt: '2024-01-12T00:00:00Z',
    updatedAt: '2024-01-12T00:00:00Z'
  },
  {
    id: 4,
    title: '线性代数',
    author: '居余马',
    description: '线性代数教材，清华大学出版社。书本整洁，适合数学专业学生。',
    price: 22.00,
    cover: 'https://img1.doubanio.com/view/subject/l/public/s1074290.jpg',
    condition: 'good',
    category: '数学',
    sellerId: 3,
    sellerName: 'student2',
    status: 'sold',
    stock: 0,
    createdAt: '2024-01-13T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z'
  },
  {
    id: 5,
    title: 'Python编程：从入门到实践',
    author: 'Eric Matthes',
    description: 'Python编程入门经典教材，适合零基础学习者。全新未拆封。',
    price: 55.00,
    cover: 'https://img1.doubanio.com/view/subject/l/public/s28659699.jpg',
    condition: 'new',
    category: '计算机',
    sellerId: 2,
    sellerName: 'student1',
    status: 'available',
    stock: 1,
    createdAt: '2024-01-14T00:00:00Z',
    updatedAt: '2024-01-14T00:00:00Z'
  }
]

// 模拟订单数据
export const mockOrders: Order[] = [
  {
    id: 1,
    bookId: 4,
    book: mockBooks[3],
    buyerId: 2,
    buyerName: 'student1',
    sellerId: 3,
    sellerName: 'student2',
    quantity: 1,
    totalPrice: 22.00,
    status: 'completed',
    createdAt: '2024-01-15T00:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  }
]

// 图书分类
export const bookCategories = [
  '数学',
  '计算机',
  '英语',
  '物理',
  '化学',
  '文学',
  '历史',
  '经济',
  '管理',
  '其他'
]
