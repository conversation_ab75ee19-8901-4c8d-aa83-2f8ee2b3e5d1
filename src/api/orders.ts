import type { Order, PaginationParams, PaginationResponse } from '@/types'
import { mockOrders, mockBooks } from '@/mock/data'

// 模拟延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// 获取我的订单（买家）
export const getMyOrders = async (params: PaginationParams): Promise<PaginationResponse<Order>> => {
  await delay(800)
  
  const token = localStorage.getItem('token')
  if (!token) {
    throw new Error('请先登录')
  }
  
  const userId = parseInt(token.split('-')[2])
  const myOrders = mockOrders.filter(order => order.buyerId === userId)
  
  // 分页
  const start = (params.page - 1) * params.pageSize
  const end = start + params.pageSize
  const list = myOrders.slice(start, end)
  
  return {
    list,
    total: myOrders.length,
    page: params.page,
    pageSize: params.pageSize
  }
}

// 获取我的销售订单（卖家）
export const getMySales = async (params: PaginationParams): Promise<PaginationResponse<Order>> => {
  await delay(800)
  
  const token = localStorage.getItem('token')
  if (!token) {
    throw new Error('请先登录')
  }
  
  const userId = parseInt(token.split('-')[2])
  const mySales = mockOrders.filter(order => order.sellerId === userId)
  
  // 分页
  const start = (params.page - 1) * params.pageSize
  const end = start + params.pageSize
  const list = mySales.slice(start, end)
  
  return {
    list,
    total: mySales.length,
    page: params.page,
    pageSize: params.pageSize
  }
}

// 创建订单
export const createOrder = async (bookId: number): Promise<Order> => {
  await delay(1000)
  
  const token = localStorage.getItem('token')
  if (!token) {
    throw new Error('请先登录')
  }
  
  const userId = parseInt(token.split('-')[2])
  const username = localStorage.getItem('username') || 'unknown'
  
  const book = mockBooks.find(b => b.id === bookId)
  if (!book) {
    throw new Error('图书不存在')
  }
  
  if (book.status !== 'available' || book.stock <= 0) {
    throw new Error('图书已售出')
  }
  
  if (book.sellerId === userId) {
    throw new Error('不能购买自己发布的图书')
  }
  
  const newOrder: Order = {
    id: mockOrders.length + 1,
    bookId: book.id,
    book: { ...book },
    buyerId: userId,
    buyerName: username,
    sellerId: book.sellerId,
    sellerName: book.sellerName,
    quantity: 1,
    totalPrice: book.price,
    status: 'completed',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
  
  mockOrders.push(newOrder)
  
  // 更新图书状态
  book.status = 'sold'
  book.stock = 0
  book.updatedAt = new Date().toISOString()
  
  return newOrder
}
