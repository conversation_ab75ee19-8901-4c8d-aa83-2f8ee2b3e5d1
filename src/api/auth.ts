import request from '@/utils/request'
import type { LoginForm, RegisterForm, User } from '@/types'
import { mockUsers } from '@/mock/data'

// 模拟延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// 登录
export const login = async (data: LoginForm): Promise<{ user: User; token: string }> => {
  await delay(1000) // 模拟网络延迟
  
  // 模拟登录验证
  const user = mockUsers.find(u => u.username === data.username)
  
  if (!user) {
    throw new Error('用户不存在')
  }
  
  // 简单的密码验证（实际项目中应该使用加密）
  if (data.password !== '123456') {
    throw new Error('密码错误')
  }
  
  return {
    user,
    token: `mock-token-${user.id}-${Date.now()}`
  }
}

// 注册
export const register = async (data: RegisterForm): Promise<{ user: User; token: string }> => {
  await delay(1000)
  
  // 检查用户名是否已存在
  const existingUser = mockUsers.find(u => u.username === data.username)
  if (existingUser) {
    throw new Error('用户名已存在')
  }
  
  // 检查邮箱是否已存在
  const existingEmail = mockUsers.find(u => u.email === data.email)
  if (existingEmail) {
    throw new Error('邮箱已被注册')
  }
  
  // 创建新用户
  const newUser: User = {
    id: mockUsers.length + 1,
    username: data.username,
    email: data.email,
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    role: 'user',
    createdAt: new Date().toISOString()
  }
  
  mockUsers.push(newUser)
  
  return {
    user: newUser,
    token: `mock-token-${newUser.id}-${Date.now()}`
  }
}

// 获取用户信息
export const getUserInfo = async (): Promise<User> => {
  await delay(500)
  
  // 从 token 中解析用户 ID（实际项目中应该从后端验证）
  const token = localStorage.getItem('token')
  if (!token) {
    throw new Error('未登录')
  }
  
  const userId = parseInt(token.split('-')[2])
  const user = mockUsers.find(u => u.id === userId)
  
  if (!user) {
    throw new Error('用户不存在')
  }
  
  return user
}
