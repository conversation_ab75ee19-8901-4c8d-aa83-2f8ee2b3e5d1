import type { Book, PublishBookForm, PaginationParams, PaginationResponse } from '@/types'
import { mockBooks, bookCategories } from '@/mock/data'

// 模拟延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// 获取图书列表
export const getBooks = async (params: PaginationParams & {
  category?: string
  keyword?: string
}): Promise<PaginationResponse<Book>> => {
  await delay(800)
  
  let filteredBooks = [...mockBooks]
  
  // 按分类筛选
  if (params.category && params.category !== '全部') {
    filteredBooks = filteredBooks.filter(book => book.category === params.category)
  }
  
  // 按关键词搜索
  if (params.keyword) {
    const keyword = params.keyword.toLowerCase()
    filteredBooks = filteredBooks.filter(book => 
      book.title.toLowerCase().includes(keyword) ||
      book.author.toLowerCase().includes(keyword) ||
      book.description.toLowerCase().includes(keyword)
    )
  }
  
  // 只显示可用的图书
  filteredBooks = filteredBooks.filter(book => book.status === 'available')
  
  // 分页
  const start = (params.page - 1) * params.pageSize
  const end = start + params.pageSize
  const list = filteredBooks.slice(start, end)
  
  return {
    list,
    total: filteredBooks.length,
    page: params.page,
    pageSize: params.pageSize
  }
}

// 获取图书详情
export const getBookDetail = async (id: number): Promise<Book> => {
  await delay(500)
  
  const book = mockBooks.find(b => b.id === id)
  if (!book) {
    throw new Error('图书不存在')
  }
  
  return book
}

// 发布图书
export const publishBook = async (data: PublishBookForm): Promise<Book> => {
  await delay(1000)
  
  // 从 token 中获取用户信息（简化处理）
  const token = localStorage.getItem('token')
  if (!token) {
    throw new Error('请先登录')
  }
  
  const userId = parseInt(token.split('-')[2])
  const username = localStorage.getItem('username') || 'unknown'
  
  const newBook: Book = {
    id: mockBooks.length + 1,
    ...data,
    sellerId: userId,
    sellerName: username,
    status: 'available',
    stock: 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
  
  mockBooks.push(newBook)
  
  return newBook
}

// 获取我发布的图书
export const getMyBooks = async (params: PaginationParams): Promise<PaginationResponse<Book>> => {
  await delay(800)
  
  const token = localStorage.getItem('token')
  if (!token) {
    throw new Error('请先登录')
  }
  
  const userId = parseInt(token.split('-')[2])
  const myBooks = mockBooks.filter(book => book.sellerId === userId)
  
  // 分页
  const start = (params.page - 1) * params.pageSize
  const end = start + params.pageSize
  const list = myBooks.slice(start, end)
  
  return {
    list,
    total: myBooks.length,
    page: params.page,
    pageSize: params.pageSize
  }
}

// 购买图书
export const buyBook = async (bookId: number): Promise<void> => {
  await delay(1000)
  
  const book = mockBooks.find(b => b.id === bookId)
  if (!book) {
    throw new Error('图书不存在')
  }
  
  if (book.status !== 'available' || book.stock <= 0) {
    throw new Error('图书已售出')
  }
  
  // 更新图书状态
  book.status = 'sold'
  book.stock = 0
  book.updatedAt = new Date().toISOString()
}

// 获取图书分类
export const getBookCategories = async (): Promise<string[]> => {
  await delay(200)
  return ['全部', ...bookCategories]
}
