<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup lang="ts">
// 应用入口组件
</script>

<style>
/* 全局样式重置和优化 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f8f9fa;
}

#app {
  min-height: 100vh;
}

/* 链接样式 */
a {
  color: #0d6efd;
  text-decoration: none;
}

a:hover {
  color: #0a58ca;
  text-decoration: underline;
}

/* Element Plus 和 Bootstrap 兼容性调整 */
.el-button {
  border-radius: 0.375rem;
}

.el-input__wrapper {
  border-radius: 0.375rem;
}

/* 自定义工具类 */
.shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.text-primary {
  color: #0d6efd !important;
}

/* 响应式容器调整 */
@media (min-width: 1400px) {
  .container, .container-lg, .container-md, .container-sm, .container-xl, .container-xxl {
    max-width: 1320px;
  }
}

@media (min-width: 1600px) {
  .container, .container-lg, .container-md, .container-sm, .container-xl, .container-xxl {
    max-width: 1500px;
  }
}
</style>
