// 用户类型
export interface User {
  id: number
  username: string
  email: string
  avatar?: string
  role: 'user' | 'admin'
  createdAt: string
}

// 图书类型
export interface Book {
  id: number
  title: string
  author: string
  description: string
  price: number
  cover: string
  condition: 'new' | 'good' | 'fair' | 'poor'
  category: string
  sellerId: number
  sellerName: string
  status: 'available' | 'sold' | 'reserved'
  stock: number
  createdAt: string
  updatedAt: string
}

// 订单类型
export interface Order {
  id: number
  bookId: number
  book: Book
  buyerId: number
  buyerName: string
  sellerId: number
  sellerName: string
  quantity: number
  totalPrice: number
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled'
  createdAt: string
  updatedAt: string
}

// 登录表单
export interface LoginForm {
  username: string
  password: string
}

// 注册表单
export interface RegisterForm {
  username: string
  email: string
  password: string
  confirmPassword: string
}

// 发布图书表单
export interface PublishBookForm {
  title: string
  author: string
  description: string
  price: number
  cover: string
  condition: 'new' | 'good' | 'fair' | 'poor'
  category: string
}

// API 响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 分页参数
export interface PaginationParams {
  page: number
  pageSize: number
}

// 分页响应
export interface PaginationResponse<T> {
  list: T[]
  total: number
  page: number
  pageSize: number
}
